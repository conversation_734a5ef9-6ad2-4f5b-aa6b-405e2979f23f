<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// DEBUG: Verificar informações da sessão
error_log("DEBUG - Informações da sessão:");
error_log("userName: " . (isset($_SESSION['userName']) ? $_SESSION['userName'] : 'não definido'));
error_log("cargo_utilizador: " . (isset($_SESSION['cargo_utilizador']) ? $_SESSION['cargo_utilizador'] : 'não definido'));
error_log("user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'não definido'));
error_log("id_utilizadores: " . (isset($_SESSION['id_utilizadores']) ? $_SESSION['id_utilizadores'] : 'não definido'));

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header("Location: Projeto.php?msg=Você não tem permissão para acessar esta página");
    exit;
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Verificar se a tabela registro_horas existe
$table_exists = false;
$result_check = mysqli_query($conn, "SHOW TABLES LIKE 'registro_horas'");
if (mysqli_num_rows($result_check) > 0) {
    $table_exists = true;
}

// Se a tabela não existir, criar
if (!$table_exists) {
    $create_table_query = "
    CREATE TABLE IF NOT EXISTS `registro_horas` (
      `id` INT NOT NULL AUTO_INCREMENT,
      `id_obra` INT NOT NULL,
      `id_usuario` INT NOT NULL,
      `horas` DECIMAL(5, 2) NOT NULL,
      `data_registro` DATE NOT NULL,
      `descricao` TEXT NULL,
      `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      CONSTRAINT `fk_registro_horas_obras`
        FOREIGN KEY (`id_obra`)
        REFERENCES `obras` (`obras_id`)
        ON DELETE CASCADE
        ON UPDATE CASCADE,
      CONSTRAINT `fk_registro_horas_usuarios`
        FOREIGN KEY (`id_usuario`)
        REFERENCES `utilizadores` (`id_utilizadores`)
        ON DELETE CASCADE
        ON UPDATE CASCADE
    )";

    mysqli_query($conn, $create_table_query);

    // Verificar novamente se a tabela foi criada
    $result_check = mysqli_query($conn, "SHOW TABLES LIKE 'registro_horas'");
    if (mysqli_num_rows($result_check) > 0) {
        $table_exists = true;
        $_SESSION['mensagem'] = "Tabela de registro de horas criada com sucesso!";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao criar tabela de registro de horas. Entre em contato com o administrador.";
        $_SESSION['tipo_mensagem'] = "danger";
    }
}

// Adicionar verificação das tabelas
if ($table_exists) {
    // Verificar se as tabelas existem e têm dados
    $check_tables = [
        "SELECT COUNT(*) as count FROM obras" => "Número de obras",
        "SELECT COUNT(*) as count FROM utilizadores" => "Número de utilizadores",
        "SELECT COUNT(*) as count FROM registro_horas" => "Número total de registos de horas"
    ];
    
    foreach ($check_tables as $query => $description) {
        $result_check = mysqli_query($conn, $query);
        if ($result_check) {
            $row = mysqli_fetch_assoc($result_check);
            error_log("$description: " . $row['count']);
        } else {
            error_log("Erro ao verificar $description: " . mysqli_error($conn));
        }
    }
    
    // Verificar se as chaves estrangeiras estão corretas
    $check_fk = "SELECT 
                    rh.id_obra, o.obras_id, 
                    rh.id_usuario, u.id_utilizadores
                 FROM 
                    registro_horas rh
                 LEFT JOIN 
                    obras o ON rh.id_obra = o.obras_id
                 LEFT JOIN 
                    utilizadores u ON rh.id_usuario = u.id_utilizadores
                 LIMIT 5";
    
    $result_fk = mysqli_query($conn, $check_fk);
    if ($result_fk) {
        error_log("Verificação de chaves estrangeiras: OK");
        while ($row = mysqli_fetch_assoc($result_fk)) {
            error_log("Registro: obra_id=" . ($row['id_obra'] ?? 'NULL') . 
                      ", obras.id=" . ($row['obras_id'] ?? 'NULL') . 
                      ", usuario_id=" . ($row['id_usuario'] ?? 'NULL') . 
                      ", utilizadores.id=" . ($row['id_utilizadores'] ?? 'NULL'));
        }
    } else {
        error_log("Erro ao verificar chaves estrangeiras: " . mysqli_error($conn));
    }
}

// Buscar registos de horas
$num_registos = 0;
$result_registos = null;

if ($table_exists) {
    // Primeiro, vamos verificar a estrutura da tabela para identificar o nome correto da coluna ID
    $result_structure = mysqli_query($conn, "DESCRIBE registro_horas");
    $id_column_name = null;
    
    if ($result_structure) {
        while ($column = mysqli_fetch_assoc($result_structure)) {
            if ($column['Key'] == 'PRI') {
                $id_column_name = $column['Field'];
                break;
            }
        }
    }
    
    // Se não conseguimos determinar o nome da coluna ID, usamos um valor padrão
    if (!$id_column_name) {
        $id_column_name = 'id';
    }
    
    // Obter o ID do usuário conectado
    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 
              (isset($_SESSION['id_utilizadores']) ? $_SESSION['id_utilizadores'] : null);
    
    // Garantir que user_id seja um valor numérico válido
    if (!is_numeric($user_id)) {
        error_log("ID do utilizador inválido: " . ($user_id ?? 'não definido'));
        $user_id = 0; // Valor que não corresponderá a nenhum usuário
    }
    
    // Consulta SQL para buscar os registos de horas
    if (isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')) {
        // Administradores (1) e gerentes (2) veem todos os registos
        $query_registos = "SELECT 
                            rh.$id_column_name as registro_id, 
                            rh.id_obra, 
                            rh.id_usuario, 
                            rh.horas, 
                            rh.data_registro, 
                            rh.descricao, 
                            o.nome_obra, 
                            u.nome_utilizador
                        FROM 
                            registro_horas rh
                        JOIN 
                            obras o ON rh.id_obra = o.obras_id
                        JOIN 
                            utilizadores u ON rh.id_usuario = u.id_utilizadores
                        ORDER BY 
                            rh.data_registro DESC";
    } else {
        // Supervisores (3) e operários (4) veem apenas seus próprios registos
        $query_registos = "SELECT 
                            rh.$id_column_name as registro_id, 
                            rh.id_obra, 
                            rh.id_usuario, 
                            rh.horas, 
                            rh.data_registro, 
                            rh.descricao, 
                            o.nome_obra, 
                            u.nome_utilizador
                        FROM 
                            registro_horas rh
                        JOIN 
                            obras o ON rh.id_obra = o.obras_id
                        JOIN 
                            utilizadores u ON rh.id_usuario = u.id_utilizadores
                        WHERE 
                            rh.id_usuario = $user_id
                        ORDER BY 
                            rh.data_registro DESC";
    }
    
    // Executar a consulta
    $result_registos = mysqli_query($conn, $query_registos);
    
    if (!$result_registos) {
        error_log("Erro na consulta: " . mysqli_error($conn) . " - Query: " . $query_registos);
    } else {
        $num_registos = mysqli_num_rows($result_registos);
    }
}

// Buscar lista de obras para o formulário
$query_obras = "SELECT obras_id, nome_obra FROM obras ORDER BY nome_obra";
$result_obras = mysqli_query($conn, $query_obras);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Horas - Built Organizer</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Ícones da biblioteca Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">

    <style>
        .action-cell {
            text-align: center;
            white-space: nowrap;
        }
        .btn-icon {
            width: 30px;
            height: 30px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .btn-edit {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-delete {
            background-color: #dc3545;
            color: white;
        }
        .btn-icon i {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php" class="active">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Mensagens de alerta -->
            <?php if(isset($_SESSION['mensagem'])): ?>
                <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['mensagem']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
                <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
            <?php endif; ?>

            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-clock-history"></i> Gestão de Horas</h1>
                    <p>Registre e gerencie as horas trabalhadas em cada obra.</p>
                </div>
            </div>

            <div class="container">
                <!-- Botões de ação -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalRegistroHoras">
                        <i class="bi bi-plus-circle"></i> Registar Horas
                    </button>
                </div>

                <!-- Tabela de Registros de Horas -->
                <div class="obras-table">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">Obra</th>
                                    <th scope="col">Funcionário</th>
                                    <th scope="col">Data</th>
                                    <th scope="col">Horas</th>
                                    <th scope="col">Descrição</th>
                                    <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')): ?>
                                    <th scope="col">Ações</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($table_exists && $result_registos && mysqli_num_rows($result_registos) > 0): ?>
                                    <?php while($registo = mysqli_fetch_assoc($result_registos)): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($registo['nome_obra'] ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($registo['nome_utilizador'] ?? 'N/A'); ?></td>
                                            <td><?php echo isset($registo['data_registro']) ? date('d/m/Y', strtotime($registo['data_registro'])) : 'N/A'; ?></td>
                                            <td><?php echo isset($registo['horas']) ? number_format((float)$registo['horas'], 1) : '0'; ?> h</td>
                                            <td><?php echo htmlspecialchars($registo['descricao'] ?? 'Sem descrição'); ?></td>
                                            
                                            <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')): ?>
                                            <td class="action-cell">
                                                <?php $registo_id = isset($registo['registro_id']) ? $registo['registro_id'] : ''; ?>
                                                
                                                <?php if($registo_id): ?>
                                                    <button type="button" class="btn-icon btn-edit" data-id="<?php echo $registo_id; ?>"
                                                            data-obra-id="<?php echo $registo['id_obra']; ?>"
                                                            data-usuario-id="<?php echo $registo['id_usuario']; ?>"
                                                            data-data="<?php echo $registo['data_registro']; ?>"
                                                            data-horas="<?php echo $registo['horas']; ?>"
                                                            data-descricao="<?php echo htmlspecialchars($registo['descricao']); ?>"
                                                            data-funcionario="<?php echo htmlspecialchars($registo['nome_utilizador']); ?>">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button type="button" class="btn-icon btn-delete" data-id="<?php echo $registo_id; ?>">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">ID não disponível</span>
                                                <?php endif; ?>
                                            </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?php 
                                            $colspan = 5; // Colunas básicas: Obra, Funcionário, Data, Horas, Descrição
                                            if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')) {
                                                $colspan += 1; // Adiciona coluna: Ações
                                            }
                                            echo $colspan;
                                        ?>" class="text-center py-3">
                                            <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')): ?>
                                                <i class="bi bi-exclamation-triangle me-2"></i> Nenhum registo de horas encontrado. Clique em "Registrar Horas" para começar.
                                            <?php else: ?>
                                                <i class="bi bi-exclamation-triangle me-2"></i> Você não possui registos de horas. Clique em "Registrar Horas" para começar.
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2025 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- Modal de Registo de Horas -->
    <div class="modal fade" id="modalRegistroHoras" tabindex="-1" aria-labelledby="modalRegistroHorasLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalRegistroHorasLabel">Novo Registo de Horas</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="formRegistroHoras" method="POST" action="processar_horas.php" onsubmit="return submitRegistroHoras(this);">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="mb-3">
                            <label for="obra_id" class="form-label">Obra</label>
                            <select class="form-select" id="obra_id" name="obra_id" required>
                                <option value="">Selecione uma obra</option>
                                <?php
                                // Reset the result pointer
                                mysqli_data_seek($result_obras, 0);
                                while ($obra = mysqli_fetch_assoc($result_obras)):
                                ?>
                                    <option value="<?php echo $obra['obras_id']; ?>">
                                        <?php echo htmlspecialchars($obra['nome_obra']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="funcionario_id" class="form-label">Funcionário</label>
                            <input type="hidden" name="funcionario_id" value="<?php echo $_SESSION['user_id']; ?>">
                            <input type="text" class="form-control" value="<?php echo $_SESSION['userName']; ?>" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="data" class="form-label">Data</label>
                            <input type="date" class="form-control" id="data" name="data"
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="horas" class="form-label">Horas Trabalhadas</label>
                            <input type="number" class="form-control" id="horas" name="horas"
                                   step="0.5" min="0.5" max="24" required>
                        </div>

                        <div class="mb-3">
                            <label for="descricao" class="form-label">Descrição</label>
                            <textarea class="form-control" id="descricao" name="descricao"
                                      rows="3" required></textarea>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-lg"></i>
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Horas - Apenas para usuários de nível 1 e 2 -->
    <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')): ?>
    <div class="modal fade" id="editarHorasModal" tabindex="-1" aria-labelledby="editarHorasModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarHorasModalLabel">Editar Registro de Horas</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarHoras" method="POST" action="processar_horas.php">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="acao" value="editar">
                        <input type="hidden" id="id_hora_edit" name="id">
                        <input type="hidden" id="funcionario_id_edit" name="funcionario_id">

                        <div class="mb-3">
                            <label for="obra_id_edit" class="form-label">Obra</label>
                            <select class="form-select" id="obra_id_edit" name="obra_id" required>
                                <option value="">Selecione uma obra</option>
                                <?php
                                // Reset the result pointer
                                mysqli_data_seek($result_obras, 0);
                                while ($obra = mysqli_fetch_assoc($result_obras)):
                                ?>
                                    <option value="<?php echo $obra['obras_id']; ?>">
                                        <?php echo htmlspecialchars($obra['nome_obra']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="funcionario_edit" class="form-label">Funcionário</label>
                            <input type="text" class="form-control" id="funcionario_edit" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="data_edit" class="form-label">Data</label>
                            <input type="date" class="form-control" id="data_edit" name="data" required>
                        </div>

                        <div class="mb-3">
                            <label for="horas_edit" class="form-label">Horas Trabalhadas</label>
                            <input type="number" class="form-control" id="horas_edit" name="horas"
                                   step="0.5" min="0.5" max="24" required>
                        </div>

                        <div class="mb-3">
                            <label for="descricao_edit" class="form-label">Descrição</label>
                            <textarea class="form-control" id="descricao_edit" name="descricao"
                                      rows="3" required></textarea>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-lg"></i>
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>




    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>

    <!-- Scripts para corrigir problemas com tokens CSRF -->
    <script src="csrf_fix.js"></script>
    <script src="csrf_init.js"></script>





    <!-- JavaScript personalizado -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar permissão para editar e excluir horas
            const cargoUtilizador = "<?php echo isset($_SESSION['cargo_utilizador']) ? $_SESSION['cargo_utilizador'] : ''; ?>";
            const podeEditar = cargoUtilizador === '1' || cargoUtilizador === '2';
            
            // Inicializar tooltips do Bootstrap
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Só inicializar os event listeners se o usuário tiver permissão
            if (podeEditar) {
                // Função para excluir horas
                function excluirHora(id) {
                    if (!id || id === '') {
                        alert('ID inválido. Não é possível excluir este registo.');
                        return;
                    }
                    
                    if (confirm('Tem certeza que deseja excluir este registo?')) {
                        window.location.href = 'excluir_horas.php?id=' + id;
                    }
                }

                // Event listeners para botões de editar
                document.querySelectorAll('.btn-edit').forEach(function(button) {
                    button.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        if (!id || id === '') {
                            alert('ID inválido. Não é possível editar este registo.');
                            return;
                        }
                        
                        // Obter dados do registro
                        const obra_id = this.getAttribute('data-obra-id');
                        const funcionario_id = this.getAttribute('data-usuario-id');
                        const data = this.getAttribute('data-data');
                        const horas = this.getAttribute('data-horas');
                        const descricao = this.getAttribute('data-descricao');
                        const funcionario = this.getAttribute('data-funcionario');
                        
                        // Preencher o formulário de edição
                        document.getElementById('id_hora_edit').value = id;
                        document.getElementById('obra_id_edit').value = obra_id;
                        document.getElementById('data_edit').value = data;
                        document.getElementById('horas_edit').value = horas;
                        document.getElementById('descricao_edit').value = descricao;
                        document.getElementById('funcionario_edit').value = funcionario;
                        document.getElementById('funcionario_id_edit').value = funcionario_id;
                        
                        // Abrir o modal
                        var editarHorasModal = new bootstrap.Modal(document.getElementById('editarHorasModal'));
                        editarHorasModal.show();
                    });
                });

                // Event listeners para botões de excluir
                document.querySelectorAll('.btn-delete').forEach(function(button) {
                    button.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        excluirHora(id);
                    });
                });
            }
        });
    </script>
</body>
</html>
<?php mysqli_close($conn); ?>
