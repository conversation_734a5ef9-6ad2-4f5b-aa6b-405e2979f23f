<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    $response = [
        'success' => false,
        'message' => "Faça login para acessar o sistema"
    ];
    echo json_encode($response);
    exit;
}

// Conectar ao banco de dados
$conn = connectToDatabase();
if (!$conn) {
    $response = [
        'success' => false,
        'message' => "Erro de conexão com o banco de dados"
    ];
    echo json_encode($response);
    exit;
}

// Função para validar os dados do orçamento
function validarDados($obra_id, $descricao, $valor, $status) {
    $erros = [];

    if (empty($obra_id)) {
        $erros[] = "É necessário selecionar uma obra";
    }

    if (empty($descricao)) {
        $erros[] = "A descrição é obrigatória";
    }

    $valor_validado = validateNumber($valor, 0.01);
    if ($valor_validado === false) {
        $erros[] = "O valor deve ser um número positivo";
    } else {
        $valor = $valor_validado;
    }

    if (empty($status)) {
        $erros[] = "O status é obrigatório";
    }

    return $erros;
}

// Verificar se a tabela orcamentos existe
$table_exists = false;
$result = mysqli_query($conn, "SHOW TABLES LIKE 'orcamentos'");
if ($result) {
    $table_exists = mysqli_num_rows($result) > 0;
}

// Se a tabela não existir, criar
if (!$table_exists) {
    $create_table_query = "
    CREATE TABLE IF NOT EXISTS `orcamentos` (
      `id` INT NOT NULL AUTO_INCREMENT,
      `obra_id` INT NOT NULL,
      `descricao` TEXT NOT NULL,
      `valor` DECIMAL(15, 2) NOT NULL,
      `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      `data_aprovacao` DATE NULL,
      `status` VARCHAR(50) DEFAULT 'Em análise',
      PRIMARY KEY (`id`),
      CONSTRAINT `fk_orcamentos_obras`
        FOREIGN KEY (`obra_id`)
        REFERENCES `obras` (`obras_id`)
        ON DELETE CASCADE
        ON UPDATE CASCADE
    )";

    if (!mysqli_query($conn, $create_table_query)) {
        $response = [
            'success' => false,
            'message' => "Erro ao criar tabela de orçamentos: " . mysqli_error($conn)
        ];
        echo json_encode($response);
        exit;
    }
}

// Processar ações via POST (adicionar/editar)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $response = [
            'success' => false,
            'message' => "Erro de segurança. Por favor, tente novamente."
        ];
        echo json_encode($response);
        exit;
    }

    $acao = $_POST['acao'] ?? '';

    // Adicionar novo orçamento
    if ($acao === 'adicionar') {
        $obra_id = isset($_POST['obra_id']) ? $_POST['obra_id'] : '';
        $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : '';
        $valor = isset($_POST['valor']) ? $_POST['valor'] : '';
        $status = isset($_POST['status']) ? $_POST['status'] : 'Em análise';

        // Validar dados
        $erros = validarDados($obra_id, $descricao, $valor, $status);

        if (empty($erros)) {
            // Inserir orçamento
            $query = "INSERT INTO orcamentos (obra_id, descricao, valor, status) VALUES (?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "isds", $obra_id, $descricao, $valor, $status);

            if (mysqli_stmt_execute($stmt)) {
                $response = [
                    'success' => true,
                    'message' => "Orçamento adicionado com sucesso!"
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => "Erro ao adicionar orçamento: " . mysqli_error($conn)
                ];
            }

            mysqli_stmt_close($stmt);
        } else {
            $response = [
                'success' => false,
                'message' => implode(", ", $erros)
            ];
        }

        echo json_encode($response);
        exit;
    }

    // Editar orçamento existente
    if ($acao === 'editar') {
        $id_orcamento = isset($_POST['id_orcamento']) ? $_POST['id_orcamento'] : '';
        $obra_id = isset($_POST['obra_id']) ? $_POST['obra_id'] : '';
        $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : '';
        $valor = isset($_POST['valor']) ? $_POST['valor'] : '';
        $status = isset($_POST['status']) ? $_POST['status'] : '';

        // Validar dados
        $erros = validarDados($obra_id, $descricao, $valor, $status);

        if (empty($id_orcamento)) {
            $erros[] = "ID do orçamento não fornecido";
        }

        if (empty($erros)) {
            // Verificar se o orçamento existe
            $query_check = "SELECT id FROM orcamentos WHERE id = ?";
            $stmt_check = mysqli_prepare($conn, $query_check);
            mysqli_stmt_bind_param($stmt_check, "i", $id_orcamento);
            mysqli_stmt_execute($stmt_check);
            mysqli_stmt_store_result($stmt_check);

            if (mysqli_stmt_num_rows($stmt_check) == 0) {
                $response = [
                    'success' => false,
                    'message' => "Orçamento não encontrado"
                ];
                echo json_encode($response);
                exit;
            }

            mysqli_stmt_close($stmt_check);

            // Atualizar orçamento
            $query = "UPDATE orcamentos SET obra_id = ?, descricao = ?, valor = ?, status = ? WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "isdsi", $obra_id, $descricao, $valor, $status, $id_orcamento);

            if (mysqli_stmt_execute($stmt)) {
                // Se o status for alterado para "Aprovado", atualizar a data de aprovação
                if ($status === 'Aprovado') {
                    $query_update_date = "UPDATE orcamentos SET data_aprovacao = CURRENT_DATE WHERE id = ? AND data_aprovacao IS NULL";
                    $stmt_update_date = mysqli_prepare($conn, $query_update_date);
                    mysqli_stmt_bind_param($stmt_update_date, "i", $id_orcamento);
                    mysqli_stmt_execute($stmt_update_date);
                    mysqli_stmt_close($stmt_update_date);
                }

                $response = [
                    'success' => true,
                    'message' => "Orçamento atualizado com sucesso!"
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => "Erro ao atualizar orçamento: " . mysqli_error($conn)
                ];
            }

            mysqli_stmt_close($stmt);
        } else {
            $response = [
                'success' => false,
                'message' => implode(", ", $erros)
            ];
        }

        echo json_encode($response);
        exit;
    }
}

// Processar ações via GET (aprovar/rejeitar)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Verificar token CSRF
    if (!isset($_GET['csrf_token']) || !verifyCSRFToken($_GET['csrf_token'])) {
        $response = [
            'success' => false,
            'message' => "Erro de segurança. Por favor, tente novamente."
        ];
        echo json_encode($response);
        exit;
    }

    $acao = $_GET['acao'] ?? '';

    if (($acao === 'aprovar' || $acao === 'rejeitar') && isset($_GET['id'])) {
        $id_orcamento = $_GET['id'];

        // Verificar se o orçamento existe
        $query_check = "SELECT id FROM orcamentos WHERE id = ?";
        $stmt_check = mysqli_prepare($conn, $query_check);
        mysqli_stmt_bind_param($stmt_check, "i", $id_orcamento);
        mysqli_stmt_execute($stmt_check);
        mysqli_stmt_store_result($stmt_check);

        if (mysqli_stmt_num_rows($stmt_check) == 0) {
            $response = [
                'success' => false,
                'message' => "Orçamento não encontrado"
            ];
            echo json_encode($response);
            exit;
        }

        mysqli_stmt_close($stmt_check);

        // Definir o novo status
        $novo_status = ($acao === 'aprovar') ? 'Aprovado' : 'Rejeitado';

        // Atualizar o status do orçamento
        $query = "UPDATE orcamentos SET status = ?";

        // Se estiver aprovando, definir a data de aprovação
        if ($acao === 'aprovar') {
            $query .= ", data_aprovacao = CURRENT_DATE";
        }

        $query .= " WHERE id = ?";

        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "si", $novo_status, $id_orcamento);

        if (mysqli_stmt_execute($stmt)) {
            $response = [
                'success' => true,
                'message' => "Orçamento " . ($acao === 'aprovar' ? 'aprovado' : 'rejeitado') . " com sucesso!"
            ];
        } else {
            $response = [
                'success' => false,
                'message' => "Erro ao " . ($acao === 'aprovar' ? 'aprovar' : 'rejeitar') . " orçamento: " . mysqli_error($conn)
            ];
        }

        mysqli_stmt_close($stmt);

        echo json_encode($response);
        exit;
    }
}

// Fechar conexão
mysqli_close($conn);
?>


